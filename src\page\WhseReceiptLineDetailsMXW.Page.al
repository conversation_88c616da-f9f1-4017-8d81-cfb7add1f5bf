page 60002 "Whse. Receipt Line Details MXW"
{
    ApplicationArea = All;
    Caption = 'Warehouse Receipt Line Details';
    PageType = List;
    SourceTable = "Warehouse Receipt Line Dtl MXW";
    UsageCategory = Lists;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Expiration Date"; Rec."Expiration Date")
                {
                }
                field("Purchase Order No."; Rec."Purchase Order No.")
                {
                }
                field("Item Tracking Info Assignd MXW"; Rec."Item Tracking Info Assignd MXW")
                {
                }
                field(Received; Rec.Received)
                {
                }
                field("Checked By MXW"; Rec."Checked By MXW")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(PrintPurchasePalletLabel)
            {
                ApplicationArea = All;
                Caption = 'Print Purchase Pallet Label';
                ToolTip = 'Print Purchase Pallet Label for selected warehouse receipt lines.';
                Image = Print;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;

                trigger OnAction()
                var
                    WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
                begin
                    CurrPage.SetSelectionFilter(WarehouseReceiptLineDtl);
                    if WarehouseReceiptLineDtl.FindSet() then
                        Report.RunModal(Report::"Purchase Pallet Label MXW", true, false, WarehouseReceiptLineDtl);
                end;
            }
        }
    }
}
