codeunit 60011 "Maxwell BOM Management MXW"
{
    procedure ConvertProductionBOMToAssemblyBOM(ItemNo: Code[20]): Boolean
    var
        Item: Record Item;
        ProductionBOMHeader: Record "Production BOM Header";
    begin
        // Validate inputs and get records
        if not ValidateItemAndProductionBOM(ItemNo, Item, ProductionBOMHeader) then
            exit(false);

        // Handle existing Assembly BOM components
        if not HandleExistingAssemblyBOM(ItemNo) then
            exit(false);

        // Confirm and perform conversion
        exit(PerformBOMConversion(Item, ProductionBOMHeader));
    end;

    local procedure ValidateItemAndProductionBOM(ItemNo: Code[20]; var Item: Record Item; var ProductionBOMHeader: Record "Production BOM Header"): Boolean
    var
        ErrorMsg: Label 'No Production BOM found for item %1.', Comment = '%1=Item No.';
    begin
        // Get the item record
        if not Item.Get(ItemNo) then
            exit(false);

        // Check if item has a production BOM assigned
        if Item."Production BOM No." = '' then begin
            Message(ErrorMsg, ItemNo);
            exit(false);
        end;

        // Get the production BOM header
        if not ProductionBOMHeader.Get(Item."Production BOM No.") then begin
            Message(ErrorMsg, ItemNo);
            exit(false);
        end;

        exit(true);
    end;

    local procedure HandleExistingAssemblyBOM(ItemNo: Code[20]): Boolean
    var
        BOMComponent: Record "BOM Component";
        ConfirmManagement: Codeunit "Confirm Management";
        ExistingAssemblyBOMMsg: Label 'Item %1 already has Assembly BOM components. Do you want to replace them?', Comment = '%1=Item No.';
    begin
        // Check if item already has assembly BOM components
        BOMComponent.SetRange("Parent Item No.", ItemNo);
        if not BOMComponent.IsEmpty() then
            if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ExistingAssemblyBOMMsg, ItemNo), false) then
                exit(false)
            else
                BOMComponent.DeleteAll(true);

        exit(true);
    end;

    local procedure PerformBOMConversion(var Item: Record Item; ProductionBOMHeader: Record "Production BOM Header"): Boolean
    var
        ProductionBOMLine: Record "Production BOM Line";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmMsg: Label 'Do you want to create an Assembly BOM from the Production BOM %1 for item %2?', Comment = '%1=Production BOM No., %2=Item No.';
        SuccessMsg: Label 'Assembly BOM successfully created from Production BOM %1 for item %2.', Comment = '%1=Production BOM No., %2=Item No.';
        LineNo: Integer;
    begin
        // Confirm the conversion
        if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmMsg, ProductionBOMHeader."No.", Item."No."), true) then
            exit(false);

        // Convert production BOM lines to assembly BOM components
        ProductionBOMLine.SetRange("Production BOM No.", ProductionBOMHeader."No.");
        ProductionBOMLine.SetRange("Version Code", ProductionBOMHeader."Version Nos.");
        if ProductionBOMLine.FindSet() then begin
            LineNo := 10000;
            repeat
                ConvertProductionBOMLineToAssemblyBOMComponent(ProductionBOMLine, Item."No.", LineNo);
                LineNo += 10000;
            until ProductionBOMLine.Next() = 0;

            // // Update item to indicate it has an assembly BOM
            // Item.Validate("Assembly BOM", true);
            // Item.Modify(true);

            Message(SuccessMsg, ProductionBOMHeader."No.", Item."No.");
            exit(true);
        end;

        exit(false);
    end;

    local procedure ConvertProductionBOMLineToAssemblyBOMComponent(ProductionBOMLine: Record "Production BOM Line"; ParentItemNo: Code[20]; LineNo: Integer)
    var
        BOMComponent: Record "BOM Component";
    begin
        // Initialize new BOM component record
        BOMComponent.Init();
        BOMComponent."Parent Item No." := ParentItemNo;
        BOMComponent."Line No." := LineNo;

        // Set basic fields
        SetBOMComponentType(BOMComponent, ProductionBOMLine);
        SetBOMComponentFields(BOMComponent, ProductionBOMLine);

        // Assembly BOM components use effective dates differently than Production BOM
        // For simplicity, we don't transfer the date fields as they have different semantics

        // Note: Resource Usage Type is only applicable for Resource type components
        // Since we're converting Production BOM items to Assembly BOM items, we skip this field

        // Insert the new BOM component
        BOMComponent.Insert(true);
    end;

    local procedure SetBOMComponentType(var BOMComponent: Record "BOM Component"; ProductionBOMLine: Record "Production BOM Line")
    begin
        case ProductionBOMLine.Type of
            ProductionBOMLine.Type::Item:
                BOMComponent.Validate(Type, BOMComponent.Type::Item);
            ProductionBOMLine.Type::"Production BOM":
                BOMComponent.Validate(Type, BOMComponent.Type::Item); // Convert sub-BOMs to items for assembly
        end;
    end;

    local procedure SetBOMComponentFields(var BOMComponent: Record "BOM Component"; ProductionBOMLine: Record "Production BOM Line")
    begin
        BOMComponent.Validate("No.", ProductionBOMLine."No.");
        BOMComponent.Validate(Description, ProductionBOMLine.Description);
        BOMComponent.Validate("Quantity per", ProductionBOMLine."Quantity per");
        BOMComponent.Validate("Unit of Measure Code", ProductionBOMLine."Unit of Measure Code");

        if ProductionBOMLine."Variant Code" <> '' then
            BOMComponent.Validate("Variant Code", ProductionBOMLine."Variant Code");

        if ProductionBOMLine.Position <> '' then
            BOMComponent.Validate(Position, ProductionBOMLine.Position);

        if ProductionBOMLine."Position 2" <> '' then
            BOMComponent.Validate("Position 2", ProductionBOMLine."Position 2");

        if ProductionBOMLine."Position 3" <> '' then
            BOMComponent.Validate("Position 3", ProductionBOMLine."Position 3");
    end;
}
