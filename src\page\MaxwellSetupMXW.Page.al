page 60000 "Maxwell Setup MXW"
{
    ApplicationArea = All;
    Caption = 'Maxwell Setup';
    PageType = Card;
    SourceTable = "Maxwell Setup MXW";
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            group("Warehouse Receipt Setup")
            {
                Caption = 'Warehouse Receipt Setup';

                // field("Package Nos."; Rec."Package Nos.")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the number series for package numbers.';
                // }
                // field("Default Warehouse Location"; Rec."Default Warehouse Location")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the default warehouse location.';
                // }
                // field("Quality Control Required"; Rec."Quality Control Required")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies whether quality control is required for warehouse receipts.';
                // }
                // field("Auto Assign Item Tracking"; Rec."Auto Assign Item Tracking")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies whether to automatically assign item tracking information.';
                // }
                // field("Quality Control Nos."; Rec."Quality Control Nos.")
                // {
                //     ApplicationArea = All;
                //     ToolTip = 'Specifies the number series for quality control documents.';
                // }
                field("Package Transfer Nos. MXW"; Rec."Package Transfer Nos. MXW")
                {
                }
                field("Package Tran. Jnl. Temp. MXW"; Rec."Package Tran. Jnl. Temp. MXW")
                {
                }
                field("Package Tran. Jnl. Batch MXW"; Rec."Package Tran. Jnl. Batch MXW")
                {
                }
            }
            group("Production Setup")
            {
                Caption = 'Production Setup';
                field("Consumption Jnl. Template MXW"; Rec."Consumption Jnl. Template MXW")
                {
                }
                field("Consumption Jnl. Batch MXW"; Rec."Consumption Jnl. Batch MXW")
                {
                }
                field("Output Journal Template MXW"; Rec."Output Journal Template MXW")
                {
                }
                field("Output Journal Batch MXW"; Rec."Output Journal Batch MXW")
                {
                }
                field("Default Output Location Code"; Rec."Default Output Location Code")
                {
                }
            }
            group("Expiration Notification Setup")
            {
                Caption = 'Expiration Notification Setup';
                field("Expiration Warning Days MXW"; Rec."Expiration Warning Days MXW")
                {
                }
                field("Notification Frequency MXW"; Rec."Notification Frequency MXW")
                {
                }
                field("Exp Notif Email MXW"; Rec."Exp Notif Email MXW")
                {
                }
                field("Last Notification Date MXW"; Rec."Last Notification Date MXW")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group("Expiration Notifications")
            {
                Caption = 'Expiration Notifications';
                action("Create Job Queue Entry")
                {
                    ApplicationArea = All;
                    Caption = 'Create Job Queue Entry';
                    ToolTip = 'Creates a job queue entry for automatic expiration notifications based on the configured frequency.';
                    Image = Create;

                    trigger OnAction()
                    var
                        MaxwellExpirationNotif: Codeunit "Maxwell Expiration Notif. MXW";
                        JobQueueCreatedMsg: Label 'Job queue entry for expiration notifications has been created successfully.';
                    begin
                        MaxwellExpirationNotif.CreateJobQueueEntry();
                        Message(JobQueueCreatedMsg);
                    end;
                }
                action("Send Test Notification")
                {
                    ApplicationArea = All;
                    Caption = 'Send Test Notification';
                    ToolTip = 'Sends a test expiration notification email to verify the configuration.';
                    Image = SendMail;

                    trigger OnAction()
                    var
                        MaxwellExpirationNotif: Codeunit "Maxwell Expiration Notif. MXW";
                        TestNotificationSentMsg: Label 'Test notification has been sent successfully.';
                    begin
                        MaxwellExpirationNotif.SendExpirationNotifications();
                        Message(TestNotificationSentMsg);
                    end;
                }
                action("Show Email Content")
                {
                    ApplicationArea = All;
                    Caption = 'Show Email Content';
                    ToolTip = 'Shows the email content that would be sent for expiration notifications without actually sending the email.';
                    Image = ViewDetails;

                    trigger OnAction()
                    var
                        MaxwellExpirationNotif: Codeunit "Maxwell Expiration Notif. MXW";
                    begin
                        MaxwellExpirationNotif.ShowEmailContentForTesting();
                    end;
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.InsertIfNotExists();
    end;
}
