codeunit 60000 "Maxwell Events MXW"
{
    var
        SecondaryCoAManagement: Codeunit "Secondary CoA Management MXW";

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterCreateWhseReceiptHeaderFromWhseRequest, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterCreateWhseReceiptHeaderFromWhseRequest"(var WhseReceiptHeader: Record "Warehouse Receipt Header"; var WarehouseRequest: Record "Warehouse Request"; var GetSourceDocuments: Report "Get Source Documents")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptHeaderFromRequest(WhseReceiptHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterGetSingleInboundDoc, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterGetSingleInboundDoc"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptHeaderFromSingleDoc(WarehouseReceiptHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterSetWarehouseRequestFilters, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterSetWarehouseRequestFilters"(var WarehouseRequest: Record "Warehouse Request"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        if WarehouseReceiptHeader."Vendor No. MXW" <> '' then
            WarehouseRequest.SetRange("Destination No.", WarehouseReceiptHeader."Vendor No. MXW");
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purchases Warehouse Mgt.", OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine, '', false, false)]
    // local procedure "Purchases Warehouse Mgt._OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; PurchaseLine: Record "Purchase Line")
    // begin
    //     WarehouseReceiptLine.Quantity := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine."Qty. (Base)" := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine.Validate("Qty. Received", 0);
    //     WarehouseReceiptLine.Validate("Qty. Outstanding", PurchaseLine."Whse. Rcpt. Qty-to Receive MXW");
    //     WarehouseReceiptLine.Validate("Qty. to Receive", 0);

    //     PurchaseLine."Whse. Rcpt. Qty-to Receive MXW" := 0;
    //     PurchaseLine.Modify(true);
    // end;

    // [EventSubscriber(ObjectType::Report, Report::"Get Source Documents", OnAfterPurchaseLineOnPreDataItem, '', false, false)]
    // local procedure "Get Source Documents_OnAfterPurchaseLineOnPreDataItem"(var Sender: Report "Get Source Documents"; var PurchaseLine: Record "Purchase Line"; OneHeaderCreated: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; WhseReceiptHeader: Record "Warehouse Receipt Header")
    // begin
    //     PurchaseLine.SetFilter("Whse. Rcpt. Qty-to Receive MXW", '>0');
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterCode, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterCode"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header"; WarehouseReceiptLine: Record "Warehouse Receipt Line"; CounterSourceDocTotal: Integer; CounterSourceDocOK: Integer)
    begin
        // Post-processing after warehouse receipt posting
        // Can be enhanced with additional business logic as needed
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnBeforeWhseReceiptLineInsert, '', false, false)]
    local procedure "Whse.-Create Source Document_OnBeforeWhseReceiptLineInsert"(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptLineOnInsert(WarehouseReceiptLine);
    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.HandleItemTrackingLinesOnAfterCopyTrackingSpec(SourceTrackingSpec, DestTrkgSpec);
    end;

    // Event subscriber to check for expired lots when entering lot no directly in tracking specification
    [EventSubscriber(ObjectType::Table, Database::"Tracking Specification", OnAfterValidateEvent, "Lot No.", false, false)]
    local procedure "Tracking Specification_OnAfterValidate_LotNo"(var Rec: Record "Tracking Specification"; var xRec: Record "Tracking Specification"; CurrFieldNo: Integer)
    var
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        // Only check if lot no was changed and is not empty
        if (Rec."Lot No." <> xRec."Lot No.") and (Rec."Lot No." <> '') then
            // For consumption operations, check if this is related to a consumption journal entry
            if this.IsConsumptionRelatedTrackingSpec(Rec) then
                BasicFuncs.ConfirmExpiredLotUsage(Rec."Item No.", Rec."Variant Code", Rec."Lot No.")
            else
                // For non-consumption operations, still validate but without consumption-specific logic
                BasicFuncs.ConfirmExpiredLotUsage(Rec."Item No.", Rec."Variant Code", Rec."Lot No.");
    end;

    // Event subscriber to check for expired lots when entering lot no in item journal lines (consumption journal)
    [EventSubscriber(ObjectType::Table, Database::"Item Journal Line", OnAfterValidateEvent, "Lot No.", false, false)]
    local procedure "Item Journal Line_OnAfterValidate_LotNo"(var Rec: Record "Item Journal Line"; var xRec: Record "Item Journal Line"; CurrFieldNo: Integer)
    var
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        // Only check if lot no was changed and is not empty, and this is a consumption entry type
        if (Rec."Lot No." <> xRec."Lot No.") and (Rec."Lot No." <> '') and (Rec."Entry Type" = Rec."Entry Type"::Consumption) then
            BasicFuncs.ConfirmExpiredLotUsage(Rec."Item No.", Rec."Variant Code", Rec."Lot No.");
    end;

    // [EventSubscriber(ObjectType::Page, Page::"Items by Location", OnAfterSetTempMatrixLocationFilters, '', false, false)]
    // local procedure "Items by Location_OnAfterSetTempMatrixLocationFilters"(var Sender: Page "Items by Location"; var TempMatrixLocation: Record Location temporary)
    // begin
    //     // Maxwell: Filter locations if needed
    // end;

    // [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnBeforeGetNeededQty, '', false, false)]
    // local procedure CalcConsumption_OnBeforeGetNeededQty(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    // var
    //     MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    // begin
    //     //MaxwellProductionManagement.HandleCalcConsumptionOnBeforeGetNeededQty(NeededQty, CalcBasedOn, ProdOrderComponent, ProductionOrder, PostingDate, IsHandled);
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OutputJnlExplRoute_OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.HandleOutputJnlExplRouteOnBeforeOutputItemJnlLineInsert(ItemJournalLine, LastOperation);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterInsertItemJnlLine, '', false, false)]
    local procedure OnAfterInsertItemJnlLine(var ItemJournalLine: Record "Item Journal Line")
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.HandleOnAfterInsertItemJnlLine(ItemJournalLine);
    end;

    // [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnAfterCreateConsumpJnlLine, '', false, false)]
    // local procedure CalcConsumption_OnAfterCreateConsumpJnlLine(LocationCode: Code[10]; BinCode: Code[20]; QtyToPost: Decimal; var ItemJournalLine: Record "Item Journal Line")
    // var
    //     MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    // begin
    //     //MaxwellProductionManagement.HandleCalcConsumptionOnAfterCreateConsumpJnlLine(LocationCode, BinCode, QtyToPost, ItemJournalLine);
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Item Ledger Entry", OnAfterInsertEvent, '', false, false)]
    local procedure OnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        MaxwellPurchaseMngt: Codeunit "Maxwell Purchase Mngt. MXW";
        MaxwellSalesMngt: Codeunit "Maxwell Sales Mngt. MXW";
        MaxwellProductionMngt: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellPurchaseMngt.HandleOnAfterInsertItemLedgerEntry(Rec, RunTrigger);
        MaxwellSalesMngt.HandleOnAfterInsertItemLedgerEntry(Rec, RunTrigger);
        MaxwellProductionMngt.HandleOnAfterInsertItemLedgerEntry(Rec);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnAfterInsertEvent, '', false, false)]
    local procedure OnAfterInsertProductionOrder(var Rec: Record "Production Order"; RunTrigger: Boolean)
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.SetDefaultOutputLocationOnProductionOrder(Rec, RunTrigger);
    end;

    // MAXWELL-29 (adapted): Use 'No. 2' and 'No. 2 Description' instead of Group fields
    [EventSubscriber(ObjectType::Table, Database::"Gen. Journal Line", OnAfterValidateEvent, "Account No.", false, false)]
    local procedure GenJnlLine_OnAfterValidate_AccountNo_No2(var Rec: Record "Gen. Journal Line"; var xRec: Record "Gen. Journal Line"; CurrFieldNo: Integer)
    begin
        SecondaryCoAManagement.TestNo2DataOnGLAccount(Rec);
        //UpdateNo2FieldsForGeneralJournalLine(Rec, xRec, CurrFieldNo);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnAfterValidateEvent, "No.", false, false)]
    local procedure SalesLine_OnAfterValidate_No_No2(var Rec: Record "Sales Line"; var xRec: Record "Sales Line"; CurrFieldNo: Integer)
    begin
        SecondaryCoAManagement.TestNo2DataOnGLAccount(Rec);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", OnBeforeRunWithCheck, '', false, false)]
    local procedure GenJnlPostLine_OnBeforeRunWithCheck_No2(var GenJournalLine2: Record "Gen. Journal Line")
    begin
        SecondaryCoAManagement.TestNo2DataOnGLAccount(GenJournalLine2);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnAfterValidateEvent, "No.", false, false)]
    local procedure PurchaseLine_OnAfterValidate_No_No2(var Rec: Record "Purchase Line"; var xRec: Record "Purchase Line"; CurrFieldNo: Integer)
    begin
        SecondaryCoAManagement.TestNo2DataOnGLAccount(Rec);
    end;

    // Event subscriber to check for expired lots during item journal posting (consumption entries)
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforePostItemJnlLine, '', false, false)]
    local procedure "ItemJnlPostLine_OnBeforePostItemJnlLine"(var ItemJournalLine: Record "Item Journal Line")
    var
        TrackingSpecification: Record "Tracking Specification";
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        // Only check consumption entries with lot numbers
        if (ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Consumption) and (ItemJournalLine."Lot No." <> '') then
            BasicFuncs.ConfirmExpiredLotUsage(ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Lot No.");

        // Also check any tracking specifications associated with this journal line
        TrackingSpecification.SetRange("Source Type", Database::"Item Journal Line");
        TrackingSpecification.SetRange("Source ID", ItemJournalLine."Journal Template Name");
        TrackingSpecification.SetRange("Source Batch Name", ItemJournalLine."Journal Batch Name");
        TrackingSpecification.SetRange("Source Ref. No.", ItemJournalLine."Line No.");
        if TrackingSpecification.FindSet() then
            repeat
                if (TrackingSpecification."Lot No." <> '') and this.IsConsumptionRelatedTrackingSpec(TrackingSpecification) then
                    BasicFuncs.ConfirmExpiredLotUsage(TrackingSpecification."Item No.", TrackingSpecification."Variant Code", TrackingSpecification."Lot No.");
            until TrackingSpecification.Next() = 0;
    end;

    // Event subscriber to check for expired lots when item tracking lines are registered
    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterRegisterItemTrackingLines, '', false, false)]
    local procedure "ItemTrackingLines_OnAfterRegisterItemTrackingLines"(var TempTrackingSpecification: Record "Tracking Specification" temporary)
    var
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        if TempTrackingSpecification.FindSet() then
            repeat
                if (TempTrackingSpecification."Lot No." <> '') and this.IsConsumptionRelatedTrackingSpec(TempTrackingSpecification) then
                    BasicFuncs.ConfirmExpiredLotUsage(TempTrackingSpecification."Item No.", TempTrackingSpecification."Variant Code", TempTrackingSpecification."Lot No.");
            until TempTrackingSpecification.Next() = 0;
    end;

    // Event subscriber to check for expired lots during item ledger entry creation (final safety net)
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post Line", OnBeforeInsertItemLedgEntry, '', false, false)]
    local procedure "ItemJnlPostLine_OnBeforeInsertItemLedgEntry"(var ItemLedgerEntry: Record "Item Ledger Entry"; ItemJournalLine: Record "Item Journal Line")
    var
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        // Final check during posting - only for consumption entries with lot numbers
        if (ItemLedgerEntry."Entry Type" = ItemLedgerEntry."Entry Type"::Consumption)
           and (ItemLedgerEntry."Lot No." <> '') then
            BasicFuncs.ConfirmExpiredLotUsage(ItemLedgerEntry."Item No.", ItemLedgerEntry."Variant Code", ItemLedgerEntry."Lot No.");
    end;

    // local procedure UpdateNo2FieldsForPurchaseLine(var Rec: Record "Purchase Line"; var xRec: Record "Purchase Line"; CurrFieldNo: Integer)
    // var
    //     GLAccount: Record "G/L Account";
    // begin
    //     Rec."No. 2 MXW" := '';
    //     Rec."No. 2 Description MXW" := '';
    //     if Rec.Type <> Rec.Type::"G/L Account" then
    //         exit;

    //     if not GLAccount.Get(Rec."No.") then
    //         exit;

    //     Rec."No. 2 MXW" := GLAccount."No. 2";
    //     Rec."No. 2 Description MXW" := GLAccount."No. 2 Description MXW";
    // end;

    // local procedure UpdateNo2FieldsForGeneralJournalLine(var Rec: Record "Gen. Journal Line"; var xRec: Record "Gen. Journal Line"; CurrFieldNo: Integer)
    // var
    //     GLAccount: Record "G/L Account";
    // begin
    //     Rec."No. 2 MXW" := '';
    //     Rec."No. 2 Description MXW" := '';
    //     if Rec."Account Type" <> Rec."Account Type"::"G/L Account" then
    //         exit;

    //     if not GLAccount.Get(Rec."Account No.") then
    //         exit;

    //     Rec."No. 2 MXW" := GLAccount."No. 2";
    //     Rec."No. 2 Description MXW" := GLAccount."No. 2 Description MXW";
    // end;

    local procedure IsConsumptionRelatedTrackingSpec(TrackingSpec: Record "Tracking Specification"): Boolean
    var
        ItemJournalLine: Record "Item Journal Line";
    begin
        // Check if this tracking specification is related to consumption operations
        case TrackingSpec."Source Type" of
            Database::"Item Journal Line":
                begin
                    // Check if the source item journal line is a consumption entry
                    ItemJournalLine.SetRange("Journal Template Name", TrackingSpec."Source ID");
                    ItemJournalLine.SetRange("Journal Batch Name", TrackingSpec."Source Batch Name");
                    ItemJournalLine.SetRange("Line No.", TrackingSpec."Source Ref. No.");
                    if ItemJournalLine.FindFirst() then
                        exit(ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Consumption);
                end;
            Database::"Prod. Order Component":
                // Production order components are consumption-related
                exit(true);
            Database::"Planning Component":
                // Planning components can be consumption-related
                exit(true);
        end;

        // For other source types, assume it's not consumption-related unless we can determine otherwise
        exit(false);
    end;
}
