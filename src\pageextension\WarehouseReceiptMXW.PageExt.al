pageextension 60001 "Warehouse Receipt MXW" extends "Warehouse Receipt"
{
    layout
    {
        addafter("No.")
        {
            field("Vendor No. MXW"; Rec."Vendor No. MXW")
            {
                ApplicationArea = All;
            }
            field("Vendor Name MXW"; Rec."Vendor Name MXW")
            {
                ApplicationArea = All;
            }
            field("Quality Control Not Processed MXW"; Rec."Quality Control Not Proc. MXW")
            {
                ApplicationArea = All;
            }
            field("Checked By MXW"; Rec."Checked By MXW")
            {
                ApplicationArea = All;
            }
        }
    }

    actions
    {
        addfirst("F&unctions")
        {
            action("AssignItemTrackingInfo MXW")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = LotInfo;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Assigns item tracking information to warehouse receipt lines.';

                trigger OnAction()
                begin
                    MaxwellPurchaseManagement.AssignItemTrackingInformationFromWarehouseReceiptHeader(Rec);
                end;
            }
            action("AcceptAllQualityControl MXW")
            {
                ApplicationArea = All;
                Caption = 'Accept All Quality Control Documents';
                Image = Approve;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Accepts all Quality Control documents related to this warehouse receipt.';

                trigger OnAction()
                begin
                    MaxwellPurchaseManagement.AcceptAllQualityControlDocuments(Rec);
                end;
            }
        }
    }

    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
}
